<script>
  import { onMount } from 'svelte';

  /** @type {{ customerCount: number; sitesCount: number; regionsCount: number; customerComputersCount: number; calculationRulesCount: number; engineVariantsCount: number; calculationsCount: number; serviceCodeActionsCount: number; productValidityGroupsCount: number; serviceCodesCount: number; serviceCodeHeaderCount: number; baseServicesCount: number; labourCostsCount: number; productDesignationsCount: number; pdpValidityCount: number; serviceCodeAndActionTypeCount: number; partNumbersStandardCount: number; labourTimeCount: number; pricelistCount: number; priceFileCount: number; priceCount: number; serviceIdCount: number; }} */
  export let data;

  // Error handling state
  let errors = [];
  let showErrorAlert = false;

  // Card data with error handling
  const mainCards = [
    { href: '/starting-page', heading: 'Starting Page', desc: 'Start the service contract quotation process with engine data input and package selection.', highlight: true },
    { href: '/workload-overview', heading: 'Workload Overview', desc: 'View and manage workload across all computers and customers.' },
    { href: '/service-id', heading: 'Service IDs', desc: 'Manage service identifiers and their associated metadata.' },
    { href: '/part-numbers-standard', heading: 'Standard Part Numbers', desc: 'View and manage standardized part numbers and their details.' },
    { href: '/liststd1?collection=customers&filterlist=name', heading: 'Customers', desc: 'View and manage customer information, computers, and service contracts.' },
    { href: '/regions', heading: 'Regions', desc: 'Manage geographical Regions and their associated service areas.' },
    { href: '/business-functions', heading: 'Business Functions', desc: 'Manage service contracts, analyze performance, and track business metrics.' },
    { href: '/business-models', heading: 'Business Models', desc: 'Configure service packages, pricing models, and business rules.' },
    { href: '/calculation-part-numbers', heading: 'Part Numbers', desc: 'Manage part numbers, calculation rules, and service configurations.' },
    { href: '/services', heading: 'Services', desc: 'Manage service definitions, activity labels, and service parameters.' },
    { href: '/product-validity-group-exploration', heading: 'Product Validity Explorer', desc: 'Explore product validity groups and their related services and actions.' },
    { href: '/service-codes', heading: 'Service Codes', desc: 'Manage service codes for maintenance and repair operations.' },
    { href: '/labour-time', heading: 'Labour Time', desc: 'Manage and track labour time records for products and services.' },
    { href: '/part-numbers-service-code-elements', heading: 'Part Numbers Service Elements', desc: 'Manage service code actions related to part numbers.' },
    { href: '/product-validity-groups', heading: 'Product Validity Groups', desc: 'Manage product validity groups and their designations.' },
    { href: '/product-designation', heading: 'Product Designations', desc: 'View and manage product designations.', highlight: true },
    { href: '/product-designation-partnumber-validitygroup', heading: 'Product Designation with Part Numbers and Validity Groups', desc: 'Manage product designations with part numbers and validity groups.', highlight: true },
    { href: '/service-code-header', heading: 'ServiceCodeHeader', desc: 'View and manage service code header information and metadata.', highlight: true },
    { href: '/engine-variants', heading: 'Engine Variants', desc: 'Manage engine variants and their component configurations.' },
    { href: '/labour-costs', heading: 'Labour Costs', desc: 'Manage and track labour costs for different service activities.' },
    { href: '/data-exploration', heading: 'Data Exploration', desc: 'Explore relationships between service codes, part numbers, and product groups.' },
    { href: '/service-types', heading: 'Service Types', desc: 'Define and manage different types of services and their configurations.' },
    { href: '/pvd', heading: 'PVD', desc: 'View and manage product validity designations and their relationships.' },
    { href: '/service-schedules', heading: 'Service Schedules', desc: 'Plan and manage service schedules and maintenance intervals.' },
    { href: '/calculations', heading: 'Calculations', desc: 'Manage customer calculations.' },
    { href: '/service-work-description', heading: 'Service Work Description', desc: 'Create and manage service work descriptions.' },
    { href: '/service-code-and-action-type/list', heading: 'Service Code & Action Types', desc: 'Manage service codes and their associated action types.' },
    { href: '/service-elements', heading: 'Service Elements', desc: 'View and filter service elements for products.' },
    { href: '/service-selection', heading: 'Service Selection', desc: 'View and compare available service packages and features.' },
    { href: '/customer-computers', heading: 'Customer Computers', desc: 'View and manage computers registered to customers.' },
    { href: '/pricelist', heading: 'Price List', desc: 'Manage price list items for services and products with full CRUD operations and filtering.' },
    { href: '/price-file', heading: 'Price File', desc: 'Manage pricing information for service items and products.' },
    { href: '/service-categories', heading: 'Service Categories', desc: 'Manage service categories to organize your price list items.' },
    { href: '/part-numbers-service-code-action', heading: 'PartNumbersServiceCodeAction', desc: 'Manage part numbers and service code actions', highlight: true },
    { href: '/liststd1ServiceCodeAndActionType?collection=ServiceCodeAndActionType&filterlist=ProductValidityGroup', heading: 'Service Code & Action Type List', desc: 'View and manage service codes and their associated action types in a detailed list format.', highlight: true },
    { href: '/service-plan-product-designation', heading: 'ServicePlanProductDesignation', desc: 'Manage service plans for product designations with hour-based scheduling and CRUD operations.', highlight: true }
  ];

  // Function to handle card errors
  function handleCardError(card, error) {
    errors.push({ card, error });
    showErrorAlert = true;
    console.error(`Error with card ${card.heading}:`, error);
  }

  // Function to dismiss error alert
  function dismissErrorAlert() {
    showErrorAlert = false;
  }
</script>

<!-- Error Alert -->
{#if showErrorAlert}
  <div class="error-alert">
    <div class="error-alert-content">
      <div class="error-alert-header">
        <h3>Warning: Some cards encountered errors</h3>
        <button class="close-button" on:click={dismissErrorAlert}>×</button>
      </div>
      <div class="error-alert-body">
        <p>The following cards encountered errors and may not display correctly:</p>
        <ul>
          {#each errors as error}
            <li>{error.card.heading}: {error.error.message || 'Unknown error'}</li>
          {/each}
        </ul>
      </div>
    </div>
  </div>
{/if}

<!-- Dashboard content starts here -->

<div class="dashboard-grid">
  <div class="dashboard-grid-inner">
    {#each mainCards as card}
      <a href={card.href} class="card modern-card" class:highlight-card={card.highlight}>
        <div class="modern-card-content">
          <div class="modern-card-heading">{card.heading}</div>
          <div class="modern-card-desc">{card.desc}</div>
        </div>
      </a>
    {/each}
    <aside class="workflow-area" style="grid-row: 1 / span 100; grid-column: 6;">
      <div class="workflow-header-bold">Workflows</div>
      <a href="/add-customer" class="workflow-row">Add a New Customer</a>
      <a href="/create-service" class="workflow-row">Create a New Service</a>
    </aside>
  </div>
</div>

<style>
  /* Dashboard header styles removed as requested */

  .dashboard-grid {
    width: 100%;
    display: flex;
    justify-content: center;
    padding: 1rem 1rem 2rem;
  }
  .dashboard-grid-inner {
    display: grid;
    grid-template-columns: repeat(5, 1fr) 1.2fr;
    gap: 1.5rem;
    width: 100%;
    max-width: 1920px;
    align-items: start;
  }
  .dashboard-grid-inner > .card {
    min-width: 0;
  }
  .modern-card {
    background: linear-gradient(135deg, #fff 70%, #f8f9fb 100%);
    border-radius: 1.1rem;
    box-shadow: 0 4px 24px rgba(0,0,0,0.07), 0 1.5px 4px rgba(0,0,0,0.05);
    border: 1.5px solid #f0f0f0;
    padding: 0;
    text-decoration: none;
    color: #0a2463;
    min-width: 0;
    transition: box-shadow 0.15s, transform 0.15s, border 0.15s;
    display: flex;
    align-items: stretch;
    cursor: pointer;
  }
  .modern-card:hover {
    box-shadow: 0 8px 32px rgba(0,0,0,0.12), 0 2.5px 8px rgba(0,0,0,0.08);
    border: 1.5px solid orangered;
    transform: translateY(-4px) scale(1.025);
  }

  /* Highlight card with light blue background */
  .highlight-card {
    background: linear-gradient(135deg, #e6f2ff 70%, #d4e6ff 100%);
    border: 1.5px solid #b3d9ff;
  }
  .highlight-card:hover {
    border: 1.5px solid #4d94ff;
    box-shadow: 0 8px 32px rgba(77, 148, 255, 0.2), 0 2.5px 8px rgba(77, 148, 255, 0.15);
  }

  .modern-card-content {
    display: flex;
    flex-direction: column;
    gap: 1.1rem;
    padding: 2.1rem 1.5rem 1.5rem 1.5rem;
    width: 100%;
  }
  .modern-card-heading {
    font-size: 1.16rem;
    font-weight: 700;
    color: #0a2463;
    margin-bottom: 0.6rem;
    letter-spacing: 0.1px;
  }
  .modern-card-desc {
    color: #4b5563;
    font-size: 1.01rem;
    font-weight: 400;
    line-height: 1.5;
    opacity: 0.89;
  }
  .workflow-area {
    background: #f8f9fb;
    border-radius: 8px;
    padding: 2rem 1.5rem 4rem 1.5rem;
    min-height: 70vh;
    box-shadow: 0 2px 16px rgba(0,0,0,0.06);
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: stretch;
    width: 100%;
    height: 100%;
    grid-row: 1 / span 100;
    grid-column: 6;
  }
  .workflow-header-bold {
    font-size: 1.25rem;
    font-weight: bold;
    color: orangered;
    margin-bottom: 1.5rem;
    text-align: left;
    letter-spacing: 0.5px;
    width: 100%;
  }
  .workflow-row {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #0a2463;
    background: none;
    border: none;
    padding: 0.75rem 0 0.75rem 0;
    margin-bottom: 0.5rem;
    text-align: left;
    text-decoration: none;
    border-radius: 0.25rem;
    transition: background 0.15s;
    width: 100%;
  }
  .workflow-row:hover {
    background: #ffe5db;
    color: orangered;
  }

  /* Error Alert Styles */
  .error-alert {
    position: fixed;
    top: 1rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    width: 90%;
    max-width: 800px;
  }

  .error-alert-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.15);
    border-left: 5px solid #ef4444;
    overflow: hidden;
  }

  .error-alert-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background-color: #fee2e2;
    color: #b91c1c;
  }

  .error-alert-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #b91c1c;
    cursor: pointer;
    padding: 0;
    margin: 0;
    line-height: 1;
  }

  .error-alert-body {
    padding: 1rem 1.5rem;
  }

  .error-alert-body p {
    margin-top: 0;
    margin-bottom: 0.75rem;
  }

  .error-alert-body ul {
    margin: 0;
    padding-left: 1.5rem;
  }

  .error-alert-body li {
    margin-bottom: 0.5rem;
  }

  /* Responsive Styles */
  @media (max-width: 1440px) {
    .dashboard-grid-inner {
      grid-template-columns: repeat(3, 1fr) 1.2fr;
    }
  }

  @media (max-width: 1024px) {
    .dashboard-grid-inner {
      grid-template-columns: 1fr;
    }
    .workflow-area {
      margin-top: 2rem;
      min-height: unset;
      width: 100%;
      height: unset;
      grid-column: 1;
      grid-row: auto;
    }
  }
</style>
