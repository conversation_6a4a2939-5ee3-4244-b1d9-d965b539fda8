import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

// LabourTime data from the provided JSON (first part)
const labourTimeDataPart1 = [
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf964"
    },
    "Service Code": "S-D5",
    "Service Description": "D5 Service Schedule S",
    "VST Code": "1700-09-00-25",
    "VST Hours": "1,6",
    "ComputerCategory": "D5",
    "ServicePhase": "S"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf965"
    },
    "Service Code": "A-D5",
    "Service Description": "D5 Service Schedule A",
    "VST Code": "1700-09-00-20",
    "VST Hours": "1,6",
    "ComputerCategory": "D5",
    "ServicePhase": "A"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf966"
    },
    "Service Code": "B-D5",
    "Service Description": "D5 Service Schedule B",
    "VST Code": "1700-09-00-25",
    "VST Hours": "1,6",
    "ComputerCategory": "D5",
    "ServicePhase": "B"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf967"
    },
    "Service Code": "C-D5",
    "Service Description": "D5 Service Schedule C",
    "VST Code": "1700-09-00-30",
    "VST Hours": "0,2",
    "ComputerCategory": "D5",
    "ServicePhase": "C"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf968"
    },
    "Service Code": "D-D5",
    "Service Description": "D5 Service Schedule D",
    "VST Code": "1700-09-00-35",
    "VST Hours": "2,6",
    "ComputerCategory": "D5",
    "ServicePhase": "D"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf969"
    },
    "Service Code": "E-D5",
    "Service Description": "D5 Service Schedule E",
    "VST Code": "1700-09-00-40",
    "VST Hours": "0,6",
    "ComputerCategory": "D5",
    "ServicePhase": "E"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf96a"
    },
    "Service Code": "F-D5",
    "Service Description": "D5 Service Schedule F",
    "VST Code": "1700-09-00-45",
    "VST Hours": "0,5",
    "ComputerCategory": "D5",
    "ServicePhase": "F"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf96c"
    },
    "Service Code": "S-D8",
    "Service Description": "D8 Service Schedule S",
    "VST Code": "1700-09-00-25",
    "VST Hours": "1,6",
    "ComputerCategory": "D8",
    "ServicePhase": "S"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf96d"
    },
    "Service Code": "A-D8",
    "Service Description": "D8 Service Schedule A",
    "VST Code": "1700-09-00-20",
    "VST Hours": "1,6",
    "ComputerCategory": "D8",
    "ServicePhase": "A"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf96e"
    },
    "Service Code": "B-D8",
    "Service Description": "D8 Service Schedule B",
    "VST Code": "1700-09-00-25",
    "VST Hours": "1,6",
    "ComputerCategory": "D8",
    "ServicePhase": "B"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf96f"
    },
    "Service Code": "C-D8",
    "Service Description": "D8 Service Schedule C",
    "VST Code": "1700-09-00-30",
    "VST Hours": "0,2",
    "ComputerCategory": "D8",
    "ServicePhase": "C"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf970"
    },
    "Service Code": "D-D8",
    "Service Description": "D8 Service Schedule D",
    "VST Code": "1700-09-00-35",
    "VST Hours": "2,6",
    "ComputerCategory": "D8",
    "ServicePhase": "D"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf971"
    },
    "Service Code": "E-D8",
    "Service Description": "D8 Service Schedule E",
    "VST Code": "1700-09-00-40",
    "VST Hours": "0,6",
    "ComputerCategory": "D8",
    "ServicePhase": "E"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf972"
    },
    "Service Code": "F-D8",
    "Service Description": "D8 Service Schedule F",
    "VST Code": "1700-09-00-45",
    "VST Hours": "0,5",
    "ComputerCategory": "D8",
    "ServicePhase": "F"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf974"
    },
    "Service Code": "S-D11",
    "Service Description": "D11 General Routine Service S",
    "VST Code": "2140-00-02-00",
    "VST Hours": "2,5",
    "ComputerCategory": "D11",
    "ServicePhase": "S"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf975"
    },
    "Service Code": "A-D11",
    "Service Description": "D11 General Routine Service A",
    "VST Code": "1700-09-00-20",
    "VST Hours": "2,3",
    "ComputerCategory": "D11",
    "ServicePhase": "A"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf976"
    },
    "Service Code": "B-D11",
    "Service Description": "D11 General Routine Service B",
    "VST Code": "2140-00-02-00",
    "VST Hours": "2,5",
    "ComputerCategory": "D11",
    "ServicePhase": "B"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf977"
    },
    "Service Code": "C-D11",
    "Service Description": "D11 General Routine Service C",
    "VST Code": "1700-09-00-27",
    "VST Hours": "0,6",
    "ComputerCategory": "D11",
    "ServicePhase": "C"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf978"
    },
    "Service Code": "D-D11",
    "Service Description": "D11 General Routine Service D",
    "VST Code": "1700-09-00-26",
    "VST Hours": 1,
    "ComputerCategory": "D11",
    "ServicePhase": "D"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf979"
    },
    "Service Code": "E-D11",
    "Service Description": "D11 General Routine Service E",
    "VST Code": "1700-09-00-30",
    "VST Hours": "0,5",
    "ComputerCategory": "D11",
    "ServicePhase": "E"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf97a"
    },
    "Service Code": "F-D11",
    "Service Description": "D11 General Routine Service F",
    "VST Code": "1700-09-00-35",
    "VST Hours": "0,5",
    "ComputerCategory": "D11",
    "ServicePhase": "F"
  }
];

// LabourTime data part 2 (remaining records)
const labourTimeDataPart2 = [
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf97c"
    },
    "Service Code": "S-D13",
    "Service Description": "D13 General Routine Service S",
    "VST Code": "2140-00-02-00",
    "VST Hours": "2,5",
    "ComputerCategory": "D13",
    "ServicePhase": "S"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf97d"
    },
    "Service Code": "A-D13",
    "Service Description": "D13 General Routine Service A",
    "VST Code": "1700-09-00-20",
    "VST Hours": "2,3",
    "ComputerCategory": "D13",
    "ServicePhase": "A"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf97e"
    },
    "Service Code": "B1-D13",
    "Service Description": "D13 General Routine Service B1",
    "VST Code": "2140-00-02-00",
    "VST Hours": "2,5",
    "ComputerCategory": "D13",
    "ServicePhase": "B1"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf97f"
    },
    "Service Code": "B2-D13",
    "Service Description": "D13 General Routine Service B2",
    "VST Code": "1700-09-00-26",
    "VST Hours": 1,
    "ComputerCategory": "D13",
    "ServicePhase": "B2"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf980"
    },
    "Service Code": "B3-D13",
    "Service Description": "D13 General Routine Service B3",
    "VST Code": "1700-09-00-27",
    "VST Hours": "0,6",
    "ComputerCategory": "D13",
    "ServicePhase": "B3"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf981"
    },
    "Service Code": "C-D13",
    "Service Description": "D13 General Routine Service C",
    "VST Code": "1700-09-00-30",
    "VST Hours": "0,5",
    "ComputerCategory": "D13",
    "ServicePhase": "C"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf982"
    },
    "Service Code": "D-D13",
    "Service Description": "D13 General Routine Service D",
    "VST Code": "1700-09-00-35",
    "VST Hours": "0,5",
    "ComputerCategory": "D13",
    "ServicePhase": "D"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf984"
    },
    "Service Code": "S-D16",
    "Service Description": "D16 General Routine Service A",
    "VST Code": "2140-00-02-00",
    "VST Hours": "2,5",
    "ComputerCategory": "D16",
    "ServicePhase": "S"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf985"
    },
    "Service Code": "A-D16",
    "Service Description": "D16 General Routine Service A",
    "VST Code": "1700-09-00-20",
    "VST Hours": "2,3",
    "ComputerCategory": "D16",
    "ServicePhase": "A"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf986"
    },
    "Service Code": "B1-D16",
    "Service Description": "D16 General Routine Service B1",
    "VST Code": "2140-00-02-00",
    "VST Hours": "2,5",
    "ComputerCategory": "D16",
    "ServicePhase": "B1"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf987"
    },
    "Service Code": "B2-D16",
    "Service Description": "D16 General Routine Service B2",
    "VST Code": "1700-09-00-26",
    "VST Hours": 1,
    "ComputerCategory": "D16",
    "ServicePhase": "B2"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf988"
    },
    "Service Code": "B3-D16",
    "Service Description": "D16 General Routine Service B3",
    "VST Code": "1700-09-00-27",
    "VST Hours": "0,6",
    "ComputerCategory": "D16",
    "ServicePhase": "B3"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf989"
    },
    "Service Code": "C-D16",
    "Service Description": "D16 General Routine Service C",
    "VST Code": "1700-09-00-30",
    "VST Hours": "0,5",
    "ComputerCategory": "D16",
    "ServicePhase": "C"
  },
  {
    "_id": {
      "$oid": "67e500c6049d0914a83cf98a"
    },
    "Service Code": "D-D16",
    "Service Description": "D16 General Routine Service D",
    "VST Code": "1700-09-00-35",
    "VST Hours": "0,5",
    "ComputerCategory": "D16",
    "ServicePhase": "D"
  }
];

// Combine all data
const allLabourTimeData = [...labourTimeDataPart1, ...labourTimeDataPart2];

async function importLabourTimeData() {
  const client = new MongoClient(uri);

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db(dbName);
    const collection = db.collection('LabourTime');

    // Clear existing data
    console.log('Clearing existing LabourTime data...');
    await collection.deleteMany({});

    // Transform and insert new data
    console.log('Inserting new LabourTime data...');
    const transformedData = allLabourTimeData.map(item => {
      // Create a new document without the problematic _id structure
      const newDoc = {
        'Service Code': item['Service Code'],
        'Service Description': item['Service Description'],
        'VST Code': item['VST Code'],
        // Convert VST Hours to proper decimal format
        'VST Hours': typeof item['VST Hours'] === 'string'
          ? parseFloat(item['VST Hours'].replace(',', '.'))
          : item['VST Hours'],
        // Ensure all required fields are present
        ServicePhase: item.ServicePhase || '',
        ComputerCategory: item.ComputerCategory || '',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Only add _id if we can convert it properly
      if (item._id && item._id.$oid) {
        try {
          newDoc._id = new ObjectId(item._id.$oid);
        } catch (e) {
          // Let MongoDB generate a new _id if conversion fails
          console.log(`Warning: Could not convert _id for ${item['Service Code']}, letting MongoDB generate new one`);
        }
      }

      return newDoc;
    });

    const result = await collection.insertMany(transformedData);
    console.log(`Successfully inserted ${result.insertedCount} LabourTime records`);

    // Create indexes for efficient querying
    console.log('Creating indexes...');
    await collection.createIndexes([
      { key: { 'Service Code': 1 } },
      { key: { ServicePhase: 1 } },
      { key: { ComputerCategory: 1 } },
      { key: { ServicePhase: 1, ComputerCategory: 1 } }, // Compound index for efficient lookup
      { key: { 'VST Code': 1 } }
    ]);

    console.log('LabourTime data import completed successfully!');

  } catch (error) {
    console.error('Error importing LabourTime data:', error);
  } finally {
    await client.close();
  }
}

// Export for use in other scripts
export { importLabourTimeData, allLabourTimeData };

// Run the import if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  importLabourTimeData();
}
