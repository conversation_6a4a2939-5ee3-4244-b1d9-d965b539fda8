<script>
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';

  /** @type {import('./$types').PageData} */
  export let data;

  // Extract data from server
  const {
    computer,
    customer,
    computerCategory,
    productDesignation,
    productValidityGroup,
    enginePackageData,
    engineVariants,
    serviceCodeData,
    servicesByLabel,
    baseServices,
    priceListData,
    businessModels,
    businessFunctions,
    calculations,
    productDesignationData,
    labourTimeData,
    labourTimeByPhase,
    dataSource
  } = data;

  // Get computerId from URL for navigation
  const computerId = $page.url.searchParams.get('computerId') || '';

  // Sample data structure based on the image layout
  let quotationList = [
    { id: 1, name: 'Sample Quotation 1', date: '2024-01-15' },
    { id: 2, name: 'Sample Quotation 2', date: '2024-01-20' }
  ];

  // Contract data - initialize with real data from computer if available
  let contractData = {
    standardContractLength: computer?.contractLength || 8760,
    extendedContractLength: computer?.extendedContractLength || 8760,
    contractAge: computer?.contractAge || computer?.HoursAtContractStart || 0,
    engineAge: computer?.engineAge || computer?.HoursAtContractStart || 0,
    engineOperatingHours: computer?.operatingHours || 1.00,
    multipleData: 1.00
  };

  // Engine specific data - calculate from real data
  let engineSpecific = {
    customerSpecific: calculateCustomerSpecific(),
    contractSpecific: calculateContractSpecific(),
    totalFixedPrice: calculateTotalFixedPrice(),
    totalVariablePrice: calculateTotalVariablePrice()
  };

  // Service data - build from real service code data
  let serviceData = buildServiceLevels();

  // Helper functions to calculate real values
  function calculateCustomerSpecific() {
    // Calculate based on customer type and region
    if (customer?.customerType === 'Fleet Owner') return 'Fleet Discount Applied';
    if (customer?.customerType === 'OEM') return 'OEM Pricing';
    return 'Standard Pricing';
  }

  function calculateContractSpecific() {
    // Calculate based on contract length and engine age
    const contractLength = contractData.standardContractLength;
    if (contractLength > 8000) return 'Extended Contract';
    if (contractLength > 4000) return 'Standard Contract';
    return 'Short Term Contract';
  }

  function calculateTotalFixedPrice() {
    // Calculate from price list data
    const fixedPrices = priceListData.filter(item =>
      item.Description && item.Description.toLowerCase().includes('fixed')
    );
    const total = fixedPrices.reduce((sum, item) => sum + (parseFloat(item["Price excl VAT"]) || 0), 0);
    return total > 0 ? `€${total.toFixed(2)}` : 'TBD';
  }

  function calculateTotalVariablePrice() {
    // Calculate from price list data
    const variablePrices = priceListData.filter(item =>
      item.Description && item.Description.toLowerCase().includes('variable')
    );
    const total = variablePrices.reduce((sum, item) => sum + (parseFloat(item["Price excl VAT"]) || 0), 0);
    return total > 0 ? `€${total.toFixed(2)}/hr` : 'TBD';
  }

  function buildServiceLevels() {
    // Use the grouped services data from ServiceCodeAndActionType collection
    const serviceLevels = [];

    // Process each service activity label from the real data
    Object.entries(servicesByLabel).forEach(([label, services]) => {
      if (services && services.length > 0) {
        // Calculate totals from real ServiceCodeAndActionType data
        const totalHours = services.reduce((sum, service) => sum + (service.InternalNoOfHours || 0), 0);
        const totalItems = services.length;
        const avgHours = totalItems > 0 ? totalHours / totalItems : 0;

        // Calculate total cost if available
        let totalCost = 0;
        services.forEach(service => {
          // Try to find price from PriceList for this service
          const priceItem = priceListData.find(price =>
            price["Part No"] === service.PartNumber?.toString() ||
            price.Description?.toLowerCase().includes(service.ServiceCode?.toLowerCase())
          );
          if (priceItem) {
            totalCost += parseFloat(priceItem["Price excl VAT"]) || 0;
          }
        });

        serviceLevels.push({
          level: label,
          service: getServiceName(label),
          available: true,
          price: avgHours > 0 ? `${avgHours.toFixed(1)} hrs` : '0.0 hrs',
          customerSpecific: `${totalItems} items`,
          contractSpecific: `${totalHours.toFixed(1)} total hrs`,
          totalCost: totalCost > 0 ? `€${totalCost.toFixed(2)}` : 'TBD',
          services: services // Keep reference to actual services
        });
      }
    });

    // Add any missing standard levels with zero data
    const standardLevels = ['A', 'B', 'C', 'D', 'E', 'S'];
    standardLevels.forEach(level => {
      if (!serviceLevels.find(sl => sl.level === level)) {
        serviceLevels.push({
          level: level,
          service: getServiceName(level),
          available: false,
          price: '0.0 hrs',
          customerSpecific: '0 items',
          contractSpecific: '0.0 total hrs',
          totalCost: 'N/A',
          services: []
        });
      }
    });

    // Sort by level (initial letter) alphabetically
    return serviceLevels.sort((a, b) => a.level.localeCompare(b.level));
  }

  function getServiceName(level) {
    const serviceNames = {
      'A': 'Base Contract Offering',
      'B': 'Dealer Add-Ons',
      'C': 'Support (Self Service)',
      'D': 'Retirement Plan',
      'E': 'Extended Support',
      'S': 'Special Services'
    };
    return serviceNames[level] || `Service Level ${level}`;
  }

  // Function to get LabourTime data for a specific level and Datortype
  function getLabourTimeForLevel(level) {
    if (!labourTimeByPhase || !labourTimeData) return null;

    // Try to find labour time data by ServicePhase matching the level
    const phaseData = labourTimeByPhase[level] || [];

    if (phaseData.length === 0) {
      // If no exact match, try to find by Service Code pattern
      const levelLabourTime = labourTimeData.filter(labour => {
        const serviceCode = labour['Service Code'] || '';
        return serviceCode.startsWith(level) || serviceCode.includes(`-${level}-`);
      });
      return levelLabourTime;
    }

    return phaseData;
  }

  // Function to calculate total LabourTime for a level
  function calculateTotalLabourTime(level) {
    const labourTimeForLevel = getLabourTimeForLevel(level);
    if (!labourTimeForLevel || labourTimeForLevel.length === 0) return 0;

    return labourTimeForLevel.reduce((total, labour) => {
      const vstHours = parseFloat(labour['VST Hours']) || 0;
      return total + vstHours;
    }, 0);
  }

  // Function to get LabourTime details for display
  function getLabourTimeDetails(level) {
    const labourTimeForLevel = getLabourTimeForLevel(level);
    if (!labourTimeForLevel || labourTimeForLevel.length === 0) {
      return {
        totalHours: 0,
        items: [],
        datortype: computerCategory || 'N/A'
      };
    }

    const totalHours = calculateTotalLabourTime(level);
    const items = labourTimeForLevel.map(labour => ({
      serviceCode: labour['Service Code'] || 'N/A',
      description: labour['Service Description'] || 'N/A',
      vstCode: labour['VST Code'] || 'N/A',
      vstHours: parseFloat(labour['VST Hours']) || 0,
      servicePhase: labour.ServicePhase || 'N/A',
      computerCategory: labour.ComputerCategory || 'N/A'
    }));

    return {
      totalHours,
      items,
      datortype: computerCategory || 'N/A'
    };
  }

  // Helper function to get price for a specific service
  function getPriceForService(service) {
    if (!service || !priceListData) return null;

    // Try to find price by Part Number first
    let priceItem = priceListData.find(price =>
      price["Part No"] === service.PartNumber?.toString()
    );

    // If not found, try by Service Code in description
    if (!priceItem && service.ServiceCode) {
      priceItem = priceListData.find(price =>
        price.Description?.toLowerCase().includes(service.ServiceCode.toLowerCase())
      );
    }

    // If not found, try by Action Type
    if (!priceItem && service.ActionType) {
      priceItem = priceListData.find(price =>
        price.Description?.toLowerCase().includes(service.ActionType.toLowerCase())
      );
    }

    return priceItem ? (parseFloat(priceItem["Price excl VAT"]) || 0).toFixed(2) : null;
  }

  // Functions to handle actions
  function addToQuotationList() {
    console.log('Add to Quotation List clicked');
  }

  function clearQuotationList() {
    quotationList = [];
  }

  function clearInputs() {
    console.log('Clear Inputs clicked');
  }

  function clearDetailed() {
    console.log('Clear Detailed Inputs Only clicked');
  }

  function goBack() {
    const url = `/subliststd1-one-page/workload-display?computerId=${computerId}&productDesignation=${productDesignation}`;
    goto(url);
  }
</script>

<svelte:head>
  <title>Engine Package Modelling - {productDesignation}</title>
</svelte:head>

<div class="engine-package-container">
  <!-- Header -->
  <div class="header">
    <button class="back-button" on:click={goBack}>
      ← Back to Workload Display
    </button>
    <h1>Engine Package Modelling</h1>
  </div>

  <!-- Customer Base Information -->
  <div class="info-section">
    <div class="customer-base-info">
      <h3>Customer Base Information</h3>
      <div class="info-grid">
        <div class="info-item">
          <label>Customer Name:</label>
          <span>{customer?.customerName || 'N/A'}</span>
        </div>
        <div class="info-item">
          <label>Customer ID:</label>
          <span>{customer?._id || 'N/A'}</span>
        </div>
        <div class="info-item">
          <label>Customer Type:</label>
          <span>{customer?.customerType || 'Fleet Owner'}</span>
        </div>
        <div class="info-item">
          <label>Region:</label>
          <span>{customer?.region || 'N/A'}</span>
        </div>
        <div class="info-item">
          <label>Country:</label>
          <span>{customer?.country || 'N/A'}</span>
        </div>
        <div class="info-item">
          <label>Contact:</label>
          <span>{customer?.contactPerson || 'N/A'}</span>
        </div>
      </div>
    </div>

    <div class="engine-base-info">
      <h3>Engine Base Information</h3>
      <div class="info-grid">
        <div class="info-item">
          <label>Product Designation:</label>
          <span>{productDesignation || 'N/A'}</span>
        </div>
        <div class="info-item">
          <label>Product Validity Group:</label>
          <span>{productValidityGroup || 'N/A'}</span>
        </div>
        <div class="info-item">
          <label>Computer ID:</label>
          <span>{computer?._id || 'N/A'}</span>
        </div>
        <div class="info-item">
          <label>Computer Model:</label>
          <span>{computer?.model || 'N/A'}</span>
        </div>
        <div class="info-item">
          <label>Computer Category:</label>
          <span>{computerCategory || 'N/A'}</span>
        </div>
        <div class="info-item">
          <label>Serial Number:</label>
          <span>{computer?.serialNumber || 'N/A'}</span>
        </div>
        <div class="info-item">
          <label>Installation Date:</label>
          <span>{computer?.installationDate ? new Date(computer.installationDate).toLocaleDateString() : 'N/A'}</span>
        </div>
        <div class="info-item">
          <label>Hours at Contract Start:</label>
          <span>{computer?.HoursAtContractStart || 'N/A'}</span>
        </div>
      </div>
    </div>
  </div>



  <!-- Main Content Grid -->
  <div class="main-grid">
    <!-- Left Column -->
    <div class="left-column">
      <!-- Contract Data Section -->
      <div class="contract-data-section">
        <h3>Contract Data</h3>
        <div class="data-grid">
          <div class="data-row">
            <label>Standard Contract Length (hrs)*</label>
            <input type="number" bind:value={contractData.standardContractLength} />
          </div>
          <div class="data-row">
            <label>Extended Contract Length (hrs)*</label>
            <input type="number" bind:value={contractData.extendedContractLength} />
          </div>
          <div class="data-row">
            <label>Contract Age (hrs)*</label>
            <input type="number" bind:value={contractData.contractAge} />
          </div>
          <div class="data-row">
            <label>Engine Age (hrs)*</label>
            <input type="number" bind:value={contractData.engineAge} />
          </div>
          <div class="data-row">
            <label>Engine Operating Hours*</label>
            <input type="number" step="0.01" bind:value={contractData.engineOperatingHours} />
          </div>
          <div class="data-row">
            <label>Multiple Data*</label>
            <input type="number" step="0.01" bind:value={contractData.multipleData} />
          </div>
        </div>
      </div>


    </div>

    <!-- Right Column -->
    <div class="right-column">
      <!-- Action Buttons -->
      <div class="action-buttons">
        <button class="action-btn add-btn" on:click={addToQuotationList}>
          Add Deliveries Quote to Quotation List
        </button>
        <button class="action-btn clear-btn" on:click={clearQuotationList}>
          Clear Quotation List
        </button>
        <button class="action-btn clear-btn" on:click={clearInputs}>
          Clear Inputs
        </button>
        <button class="action-btn clear-detailed-btn" on:click={clearDetailed}>
          Clear Detailed Inputs Only
        </button>
      </div>

      <!-- Engine Specific Section -->
      <div class="engine-specific-section">
        <h3>Engine Specific</h3>
        <div class="engine-data">
          <div class="engine-row">
            <label>Customer Specific</label>
            <span>{engineSpecific.customerSpecific}</span>
          </div>
          <div class="engine-row">
            <label>Contract Specific</label>
            <span>{engineSpecific.contractSpecific}</span>
          </div>
          <div class="engine-row">
            <label>Total Fixed Price</label>
            <span>{engineSpecific.totalFixedPrice}</span>
          </div>
          <div class="engine-row">
            <label>Total Variable Price</label>
            <span>{engineSpecific.totalVariablePrice}</span>
          </div>
        </div>
      </div>

      <!-- Quotation List -->
      <div class="quotation-list-section">
        <h3>Quotation List</h3>
        {#if quotationList.length > 0}
          <div class="quotation-items">
            {#each quotationList as item}
              <div class="quotation-item">
                <span>{item.name}</span>
                <span>{item.date}</span>
              </div>
            {/each}
          </div>
        {:else}
          <div class="no-quotations">No quotations in list</div>
        {/if}
      </div>
    </div>
  </div>

  <!-- Detailed Service Items Breakdown -->
  <div class="service-details-section">
    <h2>Service Items Detail by Activity Label</h2>

    {#each Object.entries(servicesByLabel).sort(([a], [b]) => a.localeCompare(b)) as [label, services]}
      <div class="service-label-section">
        <h3>Level {label}: {getServiceName(label)} ({services.length} items)</h3>

        {#if services.length > 0}
          <div class="service-items-table">
            <table>
              <thead>
                <tr>
                  <th>Service Code</th>
                  <th>Action Type</th>
                  <th>Activity Purpose</th>
                  <th>Part Number</th>
                  <th>Quantity</th>
                  <th>Unit of Measure</th>
                  <th>Hours</th>
                  <th>Months</th>
                  <th>Price Info</th>
                </tr>
              </thead>
              <tbody>
                {#each services as service}
                  <tr>
                    <td class="service-code">{service.ServiceCode || 'N/A'}</td>
                    <td class="action-type">{service.ActionType || 'N/A'}</td>
                    <td class="activity-purpose">{service.ActivityPurpose || 'N/A'}</td>
                    <td class="part-number">{service.PartNumber || 'N/A'}</td>
                    <td class="quantity">{service.Quantity || 0}</td>
                    <td class="unit-measure">{service.UnitOfMeasure || 'N/A'}</td>
                    <td class="hours">{service.InternalNoOfHours || 0}</td>
                    <td class="months">{service.InternalNoOfMonths || 'N/A'}</td>
                    <td class="price-info">
                      {#if getPriceForService(service)}
                        €{getPriceForService(service)}
                      {:else}
                        TBD
                      {/if}
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>

            <!-- Summary for this level -->
            <div class="level-summary">
              <p><strong>Level {label} Summary:</strong></p>
              <ul>
                <li>Total Items: {services.length}</li>
                <li>Total Hours: {services.reduce((sum, s) => sum + (s.InternalNoOfHours || 0), 0).toFixed(1)}</li>
                <li>Average Hours per Item: {(services.reduce((sum, s) => sum + (s.InternalNoOfHours || 0), 0) / services.length).toFixed(1)}</li>
                <li>Total Estimated Cost: €{services.reduce((sum, s) => sum + (parseFloat(getPriceForService(s)) || 0), 0).toFixed(2)}</li>
              </ul>
            </div>

            <!-- LabourTime Information for this level -->
            {#if getLabourTimeDetails(label).totalHours > 0 || getLabourTimeDetails(label).items.length > 0}
              <div class="labour-time-section">
                <h4>LabourTime for Level {label} - Datortype: {getLabourTimeDetails(label).datortype}</h4>
                <div class="labour-time-summary">
                  <p><strong>Total LabourTime: {getLabourTimeDetails(label).totalHours.toFixed(1)} VST Hours</strong></p>
                  <p><strong>LabourTime Items: {getLabourTimeDetails(label).items.length}</strong></p>
                </div>

                {#if getLabourTimeDetails(label).items.length > 0}
                  <div class="labour-time-details">
                    <table class="labour-time-table">
                      <thead>
                        <tr>
                          <th>Service Code</th>
                          <th>Description</th>
                          <th>VST Code</th>
                          <th>VST Hours</th>
                          <th>Service Phase</th>
                        </tr>
                      </thead>
                      <tbody>
                        {#each getLabourTimeDetails(label).items as labourItem}
                          <tr>
                            <td class="service-code">{labourItem.serviceCode}</td>
                            <td class="description">{labourItem.description}</td>
                            <td class="vst-code">{labourItem.vstCode}</td>
                            <td class="vst-hours">{labourItem.vstHours.toFixed(1)}</td>
                            <td class="service-phase">{labourItem.servicePhase}</td>
                          </tr>
                        {/each}
                      </tbody>
                    </table>
                  </div>
                {/if}
              </div>
            {:else}
              <div class="labour-time-section no-labour-time">
                <h4>LabourTime for Level {label} - Datortype: {computerCategory || 'N/A'}</h4>
                <p class="no-labour-data">No LabourTime data available for this level and Datortype combination.</p>
              </div>
            {/if}
          </div>
        {:else}
          <p class="no-services">No services found for this activity label.</p>
        {/if}
      </div>
    {/each}
  </div>

  <!-- Debug Information -->
  <div class="debug-section">
    <h3>Product-Specific Data Information</h3>
    <p><strong>Product Designation:</strong> {productDesignation}</p>
    <p><strong>Product Validity Group:</strong> {productValidityGroup}</p>
    <p><strong>Computer Category:</strong> {computerCategory}</p>
    <p><strong>Data Sources (Product-Specific):</strong></p>
    <ul>
      <li>Engine Package Modelling: {dataSource.enginePackageModelling} records</li>
      <li>Engine Variants: {dataSource.engineVariants} records</li>
      <li>Service Code Data (ServiceCodeAndActionType): {dataSource.serviceCodeData} records</li>
      <li>Service Activity Labels Found: {dataSource.servicesByLabel} labels</li>
      <li>Base Services: {dataSource.baseServices} records</li>
      <li>Price List Data: {dataSource.priceListData} records</li>
      <li>Business Models: {dataSource.businessModels} records</li>
      <li>Business Functions: {dataSource.businessFunctions} records</li>
      <li>Calculations: {dataSource.calculations} records</li>
      <li>Labour Time Data: {dataSource.labourTimeData} records</li>
      <li>Labour Time by Phase: {dataSource.labourTimeByPhase} phases</li>
    </ul>
    <p><strong>Service Levels Built from Real Data:</strong> {serviceData.length} levels</p>
    <p><strong>Service Activity Labels Detail:</strong></p>
    <ul>
      {#each Object.entries(servicesByLabel) as [label, services]}
        <li><strong>{label}:</strong> {services.length} services ({getServiceName(label)})</li>
      {/each}
    </ul>
    <p><strong>Contract Data:</strong></p>
    <ul>
      <li>Standard Contract Length: {contractData.standardContractLength} hrs</li>
      <li>Engine Age: {contractData.engineAge} hrs</li>
      <li>Customer Type: {customer?.customerType || 'N/A'}</li>
    </ul>
    <p><strong>LabourTime Data by Phase:</strong></p>
    <ul>
      {#if labourTimeByPhase && Object.keys(labourTimeByPhase).length > 0}
        {#each Object.entries(labourTimeByPhase) as [phase, labours]}
          <li><strong>{phase}:</strong> {labours.length} labour time records</li>
        {/each}
      {:else}
        <li>No LabourTime data available for this computer category</li>
      {/if}
    </ul>
  </div>
</div>

<style>
  .engine-package-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem;
    font-family: Arial, sans-serif;
  }

  .header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
  }

  .back-button {
    background: none;
    border: none;
    color: #3b82f6;
    cursor: pointer;
    font-size: 1rem;
    padding: 0.5rem 0;
  }

  .back-button:hover {
    text-decoration: underline;
  }

  h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    color: #1e293b;
  }

  /* Information Section Styles */
  .info-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .customer-base-info,
  .engine-base-info {
    background: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .customer-base-info h3,
  .engine-base-info h3 {
    margin: 0 0 1rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    border-bottom: 2px solid #3b82f6;
    padding-bottom: 0.5rem;
  }

  .info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .info-item label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
  }

  .info-item span {
    font-size: 0.875rem;
    color: #1e293b;
    background-color: #f8fafc;
    padding: 0.375rem 0.5rem;
    border-radius: 0.25rem;
    border: 1px solid #e2e8f0;
    word-break: break-all;
  }



  .main-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .left-column,
  .right-column {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .contract-data-section,
  .engine-specific-section,
  .quotation-list-section {
    background: white;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .contract-data-section h3,
  .engine-specific-section h3,
  .quotation-list-section h3 {
    margin: 0 0 1rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
  }

  .data-grid {
    display: grid;
    gap: 0.75rem;
  }

  .data-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 0.5rem;
    align-items: center;
  }

  .data-row label {
    font-size: 0.875rem;
    color: #374151;
  }

  .data-row input {
    padding: 0.375rem;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    font-size: 0.875rem;
  }



  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .action-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.25rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 0.875rem;
  }

  .add-btn {
    background-color: #10b981;
    color: white;
  }

  .add-btn:hover {
    background-color: #059669;
  }

  .clear-btn {
    background-color: #ef4444;
    color: white;
  }

  .clear-btn:hover {
    background-color: #dc2626;
  }

  .clear-detailed-btn {
    background-color: #f59e0b;
    color: white;
  }

  .clear-detailed-btn:hover {
    background-color: #d97706;
  }

  .engine-data {
    display: grid;
    gap: 0.5rem;
  }

  .engine-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 0.5rem;
    align-items: center;
    padding: 0.375rem;
    background-color: #f8fafc;
    border-radius: 0.25rem;
  }

  .engine-row label {
    font-size: 0.875rem;
    color: #374151;
  }

  .engine-row span {
    font-size: 0.875rem;
    font-weight: 500;
    color: #1e293b;
  }

  .quotation-items {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .quotation-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem;
    background-color: #f8fafc;
    border-radius: 0.25rem;
    font-size: 0.875rem;
  }

  .no-quotations {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    padding: 1rem;
  }

  /* Service Details Section Styling */
  .service-details-section {
    margin-top: 2rem;
    padding: 1rem;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #dee2e6;
  }

  .service-details-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
  }

  .service-label-section {
    margin-bottom: 2rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
  }

  .service-label-section h3 {
    background-color: #f8f9fa;
    margin: 0;
    padding: 1rem;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
  }

  .service-items-table {
    padding: 1rem;
  }

  .service-items-table table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
  }

  .service-items-table th,
  .service-items-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
  }

  .service-items-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    border-top: 1px solid #dee2e6;
  }

  .service-items-table tbody tr:hover {
    background-color: #f8f9fa;
  }

  .service-code {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #2c3e50;
  }

  .action-type {
    color: #e74c3c;
    font-weight: 500;
  }

  .activity-purpose {
    color: #27ae60;
  }

  .part-number {
    font-family: 'Courier New', monospace;
    color: #8e44ad;
  }

  .quantity,
  .hours,
  .months {
    text-align: right;
    font-weight: 500;
  }

  .price-info {
    text-align: right;
    font-weight: 600;
    color: #27ae60;
  }

  .level-summary {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    margin-top: 1rem;
  }

  .level-summary p {
    margin: 0 0 0.5rem 0;
    font-weight: 600;
    color: #495057;
  }

  .level-summary ul {
    margin: 0;
    padding-left: 1.5rem;
  }

  .level-summary li {
    margin: 0.25rem 0;
    color: #6c757d;
  }

  .no-services {
    padding: 2rem;
    text-align: center;
    color: #6c757d;
    font-style: italic;
  }

  /* LabourTime Section Styling */
  .labour-time-section {
    margin-top: 1.5rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    border-left: 4px solid #007bff;
  }

  .labour-time-section.no-labour-time {
    border-left-color: #6c757d;
    background-color: #f1f3f4;
  }

  .labour-time-section h4 {
    margin: 0 0 1rem 0;
    color: #495057;
    font-size: 1rem;
    font-weight: 600;
  }

  .labour-time-summary {
    margin-bottom: 1rem;
  }

  .labour-time-summary p {
    margin: 0.25rem 0;
    color: #495057;
    font-size: 0.9rem;
  }

  .no-labour-data {
    margin: 0;
    color: #6c757d;
    font-style: italic;
    font-size: 0.9rem;
  }

  .labour-time-details {
    margin-top: 1rem;
  }

  .labour-time-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
  }

  .labour-time-table th,
  .labour-time-table td {
    padding: 0.5rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
  }

  .labour-time-table th {
    background-color: #e9ecef;
    font-weight: 600;
    color: #495057;
    border-top: 1px solid #dee2e6;
  }

  .labour-time-table tbody tr:hover {
    background-color: #ffffff;
  }

  .labour-time-table .service-code {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #007bff;
  }

  .labour-time-table .description {
    color: #495057;
    max-width: 200px;
    word-wrap: break-word;
  }

  .labour-time-table .vst-code {
    font-family: 'Courier New', monospace;
    color: #6f42c1;
  }

  .labour-time-table .vst-hours {
    text-align: right;
    font-weight: 600;
    color: #28a745;
  }

  .labour-time-table .service-phase {
    color: #fd7e14;
    font-weight: 500;
  }

  .debug-section {
    margin-top: 2rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    border: 1px solid #ddd;
  }

  .debug-section h3 {
    color: #333;
    margin-bottom: 1rem;
  }

  .debug-section p,
  .debug-section ul {
    margin: 0.5rem 0;
    color: #666;
  }

  .debug-section ul {
    padding-left: 1.5rem;
  }

  @media (max-width: 1024px) {
    .info-section {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .info-grid {
      grid-template-columns: 1fr;
    }

    .main-grid {
      grid-template-columns: 1fr;
    }




  }

  @media (max-width: 768px) {
    .engine-package-container {
      padding: 0.5rem;
    }

    .customer-base-info,
    .engine-base-info {
      padding: 1rem;
    }

    .info-item span {
      word-break: break-word;
    }
  }
</style>
