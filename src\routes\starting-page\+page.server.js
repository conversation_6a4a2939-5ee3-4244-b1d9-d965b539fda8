import { getDatabase } from '$lib/mongodb.js';
import { error } from '@sveltejs/kit';

/** @type {import('./$types').PageServerLoad} */
export async function load() {
  try {
    // Get database connection
    const db = await getDatabase();

    // Get engine validity groups for the dropdown
    const productValidityGroupCollection = db.collection('ProductValidityGroup');
    const engineValidityGroups = await productValidityGroupCollection
      .find({})
      .sort({ ProductValidityGroup: 1 })
      .toArray();

    // Get service packages data for Step 2
    const serviceCollection = db.collection('Services');
    const servicePackages = await serviceCollection
      .find({})
      .sort({ level: 1 })
      .toArray();

    // Get base service packages structure
    const basePackages = [
      {
        level: 1,
        packageName: 'Base Contract Offering',
        pricingType: 'Fixed',
        description: 'Essential service coverage including parts supply and monitoring'
      },
      {
        level: 2,
        packageName: 'Dealer Add-Ons',
        pricingType: 'Fixed',
        description: 'Additional dealer services and support options'
      },
      {
        level: 2,
        packageName: 'Dealer Add-Ons',
        pricingType: 'Variable/hr',
        description: 'Hourly-based dealer services and maintenance'
      },
      {
        level: 3,
        packageName: 'Support (Self Service)',
        pricingType: 'Fixed',
        description: 'Self-service support tools and resources'
      },
      {
        level: '3a',
        packageName: 'Customer Package',
        pricingType: 'Fixed',
        description: 'Customized service package for specific customer needs'
      },
      {
        level: '3b',
        packageName: 'Customer Package – VODIA',
        pricingType: 'Fixed',
        description: 'VODIA-specific customer service package'
      },
      {
        level: 4,
        packageName: 'Support (Dealer)',
        pricingType: 'Fixed',
        description: 'Dealer-provided support and maintenance services'
      },
      {
        level: 4,
        packageName: 'Support (Dealer)',
        pricingType: 'Variable/hr',
        description: 'Hourly dealer support and maintenance services'
      },
      {
        level: 5,
        packageName: 'Retirement Plan',
        pricingType: 'Variable/hr',
        description: 'Engine retirement and replacement planning services'
      }
    ];

    return {
      engineValidityGroups: engineValidityGroups.map(group => ({
        id: group._id,
        name: group.ProductValidityGroup || group.name,
        description: group.description || ''
      })),
      servicePackages: basePackages,
      success: true
    };

  } catch (err) {
    console.error('Error loading starting page data:', err);
    throw error(500, 'Failed to load starting page data');
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  processEngineData: async ({ request }) => {
    try {
      const formData = await request.formData();

      const engineData = {
        engineValidityGroup: formData.get('engineValidityGroup'),
        monthlyOperatingHours: parseInt(formData.get('monthlyOperatingHours')),
        contractDuration: parseInt(formData.get('contractDuration')),
        engineStatusAtStart: formData.get('engineStatusAtStart'),
        tgwConnectivity: formData.get('tgwConnectivity') === 'on'
      };

      // Validate the data
      if (!engineData.engineValidityGroup || !engineData.monthlyOperatingHours || !engineData.contractDuration) {
        return {
          success: false,
          error: 'Please fill in all required fields'
        };
      }

      // Store the engine data in session or return it for the next step
      return {
        success: true,
        engineData,
        message: 'Engine data processed successfully. Please proceed to package selection.'
      };

    } catch (err) {
      console.error('Error processing engine data:', err);
      return {
        success: false,
        error: 'Failed to process engine data'
      };
    }
  },

  generateQuotation: async ({ request }) => {
    try {
      const formData = await request.formData();

      const quotationData = {
        engineData: JSON.parse(formData.get('engineData')),
        selectedPackages: JSON.parse(formData.get('selectedPackages')),
        createdAt: new Date()
      };

      // Here you would typically save to database and generate the actual quotation
      // For now, we'll just return success

      return {
        success: true,
        quotationData,
        message: 'Quotation generated successfully!'
      };

    } catch (err) {
      console.error('Error generating quotation:', err);
      return {
        success: false,
        error: 'Failed to generate quotation'
      };
    }
  }
};
