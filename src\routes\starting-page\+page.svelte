<script>
  import { enhance } from '$app/forms';
  import { page } from '$app/stores';

  /** @type {import('./$types').PageData} */
  export let data;
  /** @type {import('./$types').ActionData} */
  export let form;

  // Extract data from server
  const { engineValidityGroups, servicePackages } = data;

  // Component state
  let currentStep = 1;
  let customerData = {};
  let engineData = {};
  let usageData = {};
  let selectedPackages = [];
  let isSubmitting = false;

  // Form data for Step 1 - Customer and Operating Environment
  let step1FormData = {
    customerType: '',
    operatingEnvironment: ''
  };

  // Form data for Step 2 - Engine Details
  let step2FormData = {
    engineValidityGroup: '',
    engineStatusAtStart: '',
    tgwConnectivity: false
  };

  // Form data for Step 3 - Usage and Contract Parameters
  let step3FormData = {
    monthlyOperatingHours: '',
    contractDuration: ''
  };

  // Handle step navigation
  function nextStep() {
    if (currentStep < 4) {
      currentStep++;
    }
  }

  function previousStep() {
    if (currentStep > 1) {
      currentStep--;
    }
  }

  // Handle Step 1 submission
  function handleStep1Submit() {
    if (!step1FormData.customerType || !step1FormData.operatingEnvironment) {
      alert('Please fill in all required fields');
      return;
    }
    customerData = { ...step1FormData };
    nextStep();
  }

  // Handle Step 2 submission
  function handleStep2Submit() {
    if (!step2FormData.engineValidityGroup || !step2FormData.engineStatusAtStart) {
      alert('Please fill in all required fields');
      return;
    }
    engineData = { ...step2FormData };
    nextStep();
  }

  // Handle Step 3 submission
  function handleStep3Submit() {
    if (!step3FormData.monthlyOperatingHours || !step3FormData.contractDuration) {
      alert('Please fill in all required fields');
      return;
    }
    usageData = { ...step3FormData };
    nextStep();
  }

  // Handle package selection
  function togglePackage(packageIndex) {
    const packageId = `package_${packageIndex}`;
    if (selectedPackages.includes(packageId)) {
      selectedPackages = selectedPackages.filter(id => id !== packageId);
    } else {
      selectedPackages = [...selectedPackages, packageId];
    }
  }

  // Handle final quotation generation
  function handleQuotationSubmit() {
    const allData = {
      customerData,
      engineData,
      usageData,
      selectedPackages
    };
    console.log('Generating quotation with data:', allData);
    alert('Quotation generated successfully!');
  }
</script>

<div class="starting-page">
  <div class="page-header">
    <h1>Service Contract Quotation</h1>
    <p>Create a comprehensive service contract quotation in four simple steps</p>
  </div>

  <!-- Step Indicator -->
  <div class="step-indicator">
    <div class="step" class:active={currentStep === 1} class:completed={currentStep > 1}>
      <div class="step-number">1</div>
      <div class="step-title">Define Customer & Operating Environment</div>
    </div>
    <div class="step-connector" class:completed={currentStep > 1}></div>
    <div class="step" class:active={currentStep === 2} class:completed={currentStep > 2}>
      <div class="step-number">2</div>
      <div class="step-title">Define the Engine</div>
    </div>
    <div class="step-connector" class:completed={currentStep > 2}></div>
    <div class="step" class:active={currentStep === 3} class:completed={currentStep > 3}>
      <div class="step-number">3</div>
      <div class="step-title">Input Usage & Contract Parameters</div>
    </div>
    <div class="step-connector" class:completed={currentStep > 3}></div>
    <div class="step" class:active={currentStep === 4}>
      <div class="step-number">4</div>
      <div class="step-title">Select Contract Components</div>
    </div>
  </div>

  {#if currentStep === 1}
    <!-- Step 1: Define Customer and Operating Environment -->
    <div class="step-content">
      <div class="step-header">
        <h2>🔧 Step 1: Define the Customer and Operating Environment</h2>
        <p>Start by identifying the customer (operator) and the environment in which the engine will be used. This helps tailor the contract to real-world usage conditions.</p>
      </div>

      <div class="step-table">
        <table class="input-table">
          <thead>
            <tr>
              <th>Input Field</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="field-cell">
                <label for="customerType">Customer Type</label>
                <select
                  id="customerType"
                  bind:value={step1FormData.customerType}
                  required
                >
                  <option value="">Who is the operator? (e.g., fleet owner, OEM, end-user)</option>
                  <option value="fleet_owner">Fleet Owner</option>
                  <option value="oem">OEM</option>
                  <option value="end_user">End User</option>
                  <option value="leasing_company">Leasing Company</option>
                  <option value="rental_company">Rental Company</option>
                </select>
              </td>
              <td class="description-cell">Who is the operator? (e.g., fleet owner, OEM, end-user)</td>
            </tr>
            <tr>
              <td class="field-cell">
                <label for="operatingEnvironment">Operating Environment</label>
                <select
                  id="operatingEnvironment"
                  bind:value={step1FormData.operatingEnvironment}
                  required
                >
                  <option value="">Where and how will the engine be used? (e.g., marine, mining, agriculture)</option>
                  <option value="marine">Marine</option>
                  <option value="mining">Mining</option>
                  <option value="agriculture">Agriculture</option>
                  <option value="construction">Construction</option>
                  <option value="power_generation">Power Generation</option>
                  <option value="industrial">Industrial</option>
                  <option value="transportation">Transportation</option>
                </select>
              </td>
              <td class="description-cell">Where and how will the engine be used? (e.g., marine, mining, agriculture)</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="form-actions">
        <button type="button" class="btn btn-primary" on:click={handleStep1Submit}>
          Continue to Engine Definition
        </button>
      </div>
    </div>
  {/if}

  {#if currentStep === 2}
    <!-- Step 2: Define the Engine -->
    <div class="step-content">
      <div class="step-header">
        <h2>🔧 Step 2: Define the Engine</h2>
        <p>Next, specify the engine details. This determines the baseline for service needs and pricing.</p>
      </div>

      <div class="step-table">
        <table class="input-table">
          <thead>
            <tr>
              <th>Input Field</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="field-cell">
                <label for="engineValidityGroup">Engine Validity Group</label>
                <select
                  id="engineValidityGroup"
                  bind:value={step2FormData.engineValidityGroup}
                  required
                >
                  <option value="">Select the appropriate engine group based on size, type, or application</option>
                  {#each engineValidityGroups as group}
                    <option value={group.name}>{group.name}</option>
                  {/each}
                </select>
              </td>
              <td class="description-cell">Select the appropriate engine group based on size, type, or application.</td>
            </tr>
            <tr>
              <td class="field-cell">
                <label for="engineStatusAtStart">Engine Status at Start</label>
                <input
                  type="text"
                  id="engineStatusAtStart"
                  bind:value={step2FormData.engineStatusAtStart}
                  placeholder="Enter the engine's age and total operating hours at the beginning of the contract"
                  required
                />
              </td>
              <td class="description-cell">Enter the engine's age and total operating hours at the beginning of the contract.</td>
            </tr>
            <tr>
              <td class="field-cell">
                <label class="checkbox-label">
                  <input
                    type="checkbox"
                    bind:checked={step2FormData.tgwConnectivity}
                  />
                  <span class="checkbox-text">TGW Connectivity</span>
                </label>
              </td>
              <td class="description-cell">Indicate whether the engine is connected via TGW (Telematics Gateway).</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="form-actions">
        <button type="button" class="btn btn-secondary" on:click={previousStep}>
          Back to Customer Definition
        </button>
        <button type="button" class="btn btn-primary" on:click={handleStep2Submit}>
          Continue to Usage Parameters
        </button>
      </div>
    </div>
  {/if}

  {#if currentStep === 3}
    <!-- Step 3: Input Usage and Contract Parameters -->
    <div class="step-content">
      <div class="step-header">
        <h2>📊 Step 3: Input Usage and Contract Parameters</h2>
        <p>Now enter the expected usage and contract duration. These values influence the service intensity and pricing model.</p>
      </div>

      <div class="step-table">
        <table class="input-table">
          <thead>
            <tr>
              <th>Input Field</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="field-cell">
                <label for="monthlyOperatingHours">Monthly Operating Hours</label>
                <input
                  type="number"
                  id="monthlyOperatingHours"
                  bind:value={step3FormData.monthlyOperatingHours}
                  placeholder="Estimate how many hours the engine will run per month"
                  required
                />
              </td>
              <td class="description-cell">Estimate how many hours the engine will run per month.</td>
            </tr>
            <tr>
              <td class="field-cell">
                <label for="contractDuration">Contract Duration</label>
                <input
                  type="number"
                  id="contractDuration"
                  bind:value={step3FormData.contractDuration}
                  placeholder="Specify the number of years the service contract will cover"
                  required
                />
              </td>
              <td class="description-cell">Specify the number of years the service contract will cover.</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="form-actions">
        <button type="button" class="btn btn-secondary" on:click={previousStep}>
          Back to Engine Definition
        </button>
        <button type="button" class="btn btn-primary" on:click={handleStep3Submit}>
          Continue to Package Selection
        </button>
      </div>
    </div>
  {/if}

  {#if currentStep === 4}
    <!-- Step 4: Select Contract Components -->
    <div class="step-content">
      <div class="step-header">
        <h2>📦 Step 4: Select Contract Components ("Packages")</h2>
        <p>Finally, choose which service components to include in the contract. These are grouped into levels and can be fixed or variable based on usage.</p>
      </div>

      <!-- Display Configuration Summary -->
      <div class="engine-data-summary">
        <h3>Configuration Summary</h3>
        <div class="summary-grid">
          <div class="summary-item">
            <span class="label">Customer Type:</span>
            <span class="value">{customerData.customerType || 'Not specified'}</span>
          </div>
          <div class="summary-item">
            <span class="label">Operating Environment:</span>
            <span class="value">{customerData.operatingEnvironment || 'Not specified'}</span>
          </div>
          <div class="summary-item">
            <span class="label">Engine Validity Group:</span>
            <span class="value">{engineData.engineValidityGroup || 'Not specified'}</span>
          </div>
          <div class="summary-item">
            <span class="label">Engine Status at Start:</span>
            <span class="value">{engineData.engineStatusAtStart || 'Not specified'}</span>
          </div>
          <div class="summary-item">
            <span class="label">TGW Connectivity:</span>
            <span class="value">{engineData.tgwConnectivity ? 'Yes' : 'No'}</span>
          </div>
          <div class="summary-item">
            <span class="label">Monthly Operating Hours:</span>
            <span class="value">{usageData.monthlyOperatingHours || 'Not specified'}</span>
          </div>
          <div class="summary-item">
            <span class="label">Contract Duration:</span>
            <span class="value">{usageData.contractDuration ? usageData.contractDuration + ' years' : 'Not specified'}</span>
          </div>
        </div>
      </div>

      <!-- Package Selection Table -->
      <div class="package-selection">
        <table class="package-table">
          <thead>
            <tr>
              <th>Level</th>
              <th>Package Name</th>
              <th>Pricing Type</th>
              <th>Description</th>
              <th>Select</th>
            </tr>
          </thead>
          <tbody>
            {#each servicePackages as servicePackage, index}
              <tr class="package-row">
                <td class="level-cell">{servicePackage.level}</td>
                <td class="package-name-cell">{servicePackage.packageName}</td>
                <td class="pricing-type-cell">
                  <span class="pricing-badge" class:fixed={servicePackage.pricingType === 'Fixed'} class:variable={servicePackage.pricingType.includes('Variable')}>
                    {servicePackage.pricingType}
                  </span>
                </td>
                <td class="description-cell">{servicePackage.description}</td>
                <td class="select-cell">
                  <label class="package-checkbox">
                    <input
                      type="checkbox"
                      checked={selectedPackages.includes(`package_${index}`)}
                      on:change={() => togglePackage(index)}
                    />
                    <span class="checkmark"></span>
                  </label>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>

      <div class="form-actions">
        <button type="button" class="btn btn-secondary" on:click={previousStep}>
          Back to Usage Parameters
        </button>

        <button type="button" class="btn btn-primary" on:click={handleQuotationSubmit} disabled={selectedPackages.length === 0}>
          Generate Quotation ({selectedPackages.length} packages selected)
        </button>
      </div>
    </div>
  {/if}
</div>

<style>
  .starting-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }

  .page-header {
    text-align: center;
    margin-bottom: 3rem;
  }

  .page-header h1 {
    color: #0a2463;
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
  }

  .page-header p {
    color: #6b7280;
    font-size: 1.2rem;
  }

  /* Step Indicator */
  .step-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 3rem;
  }

  .step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .step-number {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background-color: #e5e7eb;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
  }

  .step.active .step-number {
    background-color: #3b82f6;
    color: white;
  }

  .step.completed .step-number {
    background-color: #10b981;
    color: white;
  }

  .step-title {
    font-weight: 600;
    color: #374151;
    max-width: 150px;
  }

  .step-connector {
    width: 4rem;
    height: 2px;
    background-color: #e5e7eb;
    margin: 0 1rem;
    transition: all 0.3s ease;
  }

  .step-connector.completed {
    background-color: #10b981;
  }

  /* Step Content */
  .step-content {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .step-header {
    margin-bottom: 2rem;
  }

  .step-header h2 {
    color: #0a2463;
    font-size: 1.8rem;
    margin-bottom: 1rem;
  }

  .step-header p {
    color: #6b7280;
    font-size: 1.1rem;
    line-height: 1.6;
  }

  /* Form Styles */
  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
  }

  .form-group.checkbox-group {
    grid-column: 1 / -1;
  }

  .form-group label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  .form-group input,
  .form-group select {
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.2s ease;
  }

  .form-group input:focus,
  .form-group select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .field-description {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.25rem;
    font-style: italic;
  }

  .checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .checkbox-label input[type="checkbox"] {
    margin-right: 0.5rem;
  }

  .checkbox-text {
    font-weight: 600;
  }

  /* Engine Data Summary */
  .engine-data-summary {
    background: #f8fafc;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .engine-data-summary h3 {
    color: #0a2463;
    margin-bottom: 1rem;
  }

  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }

  .summary-item {
    display: flex;
    justify-content: space-between;
  }

  .summary-item .label {
    font-weight: 600;
    color: #374151;
  }

  .summary-item .value {
    color: #6b7280;
  }

  /* Input Table Styles */
  .step-table {
    margin-bottom: 2rem;
  }

  .input-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 2rem;
  }

  .input-table th,
  .input-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
    vertical-align: top;
  }

  .input-table th {
    background-color: #f9fafb;
    font-weight: 600;
    color: #374151;
  }

  .field-cell {
    width: 40%;
  }

  .field-cell label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  .field-cell select,
  .field-cell input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.2s ease;
  }

  .field-cell select:focus,
  .field-cell input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .description-cell {
    color: #6b7280;
    font-style: italic;
  }

  /* Package Selection Table */
  .package-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 2rem;
  }

  .package-table th,
  .package-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }

  .package-table th {
    background-color: #f9fafb;
    font-weight: 600;
    color: #374151;
  }

  .package-row:hover {
    background-color: #f9fafb;
  }

  .pricing-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 600;
  }

  .pricing-badge.fixed {
    background-color: #dbeafe;
    color: #1e40af;
  }

  .pricing-badge.variable {
    background-color: #fef3c7;
    color: #92400e;
  }

  .package-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .package-checkbox input[type="checkbox"] {
    margin-right: 0.5rem;
  }

  /* Form Actions */
  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    align-items: center;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .btn-primary {
    background-color: #3b82f6;
    color: white;
  }

  .btn-primary:hover:not(:disabled) {
    background-color: #2563eb;
  }

  .btn-primary:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
  }

  .btn-secondary {
    background-color: #6b7280;
    color: white;
  }

  .btn-secondary:hover {
    background-color: #4b5563;
  }

  .error-message {
    background-color: #fef2f2;
    color: #dc2626;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border: 1px solid #fecaca;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .form-grid {
      grid-template-columns: 1fr;
    }

    .step-indicator {
      flex-direction: column;
      gap: 1rem;
    }

    .step-connector {
      width: 2px;
      height: 2rem;
      margin: 0;
    }

    .package-table {
      font-size: 0.875rem;
    }

    .package-table th,
    .package-table td {
      padding: 0.5rem;
    }

    .form-actions {
      flex-direction: column;
      align-items: stretch;
    }
  }
</style>
